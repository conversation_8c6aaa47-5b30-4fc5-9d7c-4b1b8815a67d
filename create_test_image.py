#!/usr/bin/env python3
"""
创建测试图像
"""

import cv2
import numpy as np

def create_test_image():
    """创建一个简单的测试图像"""
    # 创建白色背景
    img = np.ones((100, 300, 3), dtype=np.uint8) * 255
    
    # 添加一些简单的矩形模拟文字
    cv2.rectangle(img, (20, 30), (60, 70), (0, 0, 0), 2)  # 模拟'测'
    cv2.rectangle(img, (80, 30), (120, 70), (0, 0, 0), 2)  # 模拟'试'
    cv2.rectangle(img, (140, 30), (180, 70), (0, 0, 0), 2)  # 模拟'字'
    cv2.rectangle(img, (200, 30), (240, 70), (0, 0, 0), 2)  # 模拟'符'
    
    # 保存图像
    cv2.imwrite('test_image.png', img)
    print('测试图像已创建: test_image.png')

if __name__ == "__main__":
    create_test_image()
